(()=>{"use strict";const e=wc.wcBlocksRegistry,t=wc.wcSettings,a=wc.wcBlocksData,n=window.wp.htmlEntities,o=window.wp.i18n,r=window.wp.element,i=window.wp.data,l=new class{constructor(){this.containers=new Map,this.activeContainers=new Set,this.isInitialized=!1}getOrCreateContainer(e,t){const a=`${e}_${t}`;if(!this.containers.has(a)){const e=document.createElement("div");e.id=t,e.className="primer-checkout-persistent-container",e.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",document.body.appendChild(e),this.containers.set(a,{element:e,isInitialized:!1,isVisible:!1,orderId:null,clientToken:null})}return this.containers.get(a)}showContainer(e,t,a){const n=`${e}_${t}`,o=this.containers.get(n);o&&o.element&&(this.hideAllContainers(),a&&(a.appendChild(o.element),o.element.style.cssText="\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                ",o.isVisible=!0,this.activeContainers.add(n)))}hideContainer(e,t){const a=`${e}_${t}`,n=this.containers.get(a);n&&n.element&&(document.body.appendChild(n.element),n.element.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",n.isVisible=!1,this.activeContainers.delete(a))}hideAllContainers(){this.containers.forEach(((e,t)=>{e.isVisible&&this.hideContainer(t.split("_")[0],t.split("_")[1])}))}clearOrderData(e,t){const a=`${e}_${t}`,n=this.containers.get(a);n&&(n.orderId=null,n.clientToken=null,n.instructions=null,n.isInitialized=!1)}setContainerInitialized(e,t){const a=`${e}_${t}`,n=this.containers.get(a);n&&(n.isInitialized=!0)}isContainerInitialized(e,t){const a=`${e}_${t}`,n=this.containers.get(a);return!!n&&n.isInitialized}setOrderData(e,t,a,n,o=null){const r=`${e}_${t}`,i=this.containers.get(r);i?(i.orderId=a,i.clientToken=n,i.instructions=o):console.warn(`[PrimerCheckoutManager] Container ${r} not found for setting order data`)}getOrderData(e,t){const a=`${e}_${t}`,n=this.containers.get(a);return n?{orderId:n.orderId,clientToken:n.clientToken,instructions:n.instructions}:{orderId:null,clientToken:null,instructions:null}}getContainerElement(e,t){const a=`${e}_${t}`,n=this.containers.get(a);return n?n.element:null}cleanup(){this.containers.forEach((e=>{e.element&&e.element.parentNode&&e.element.parentNode.removeChild(e.element)})),this.containers.clear(),this.activeContainers.clear()}};"undefined"!=typeof window&&window.addEventListener("beforeunload",(()=>{l.cleanup()}));if("undefined"!=typeof document){const e=document.createElement("style");e.type="text/css",e.innerText="\n@keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n}\n",document.head.appendChild(e)}if("function"!=typeof e.registerPaymentMethod)console.warn("Monoova PayTo Block: registerPaymentMethod not available. Available globals:",Object.keys(window.wc||{}));else{let c={};if("function"==typeof t.getSetting)try{c=(0,t.getSetting)("monoova_payto_data",{})}catch(e){console.log("Monoova PayTo Block: getSetting failed:",e)}c&&0!==Object.keys(c).length||(c=window.monoova_payto_blocks_params||{},console.log("Monoova PayTo Block: Using fallback settings:",c)),c&&0!==Object.keys(c).length||(console.warn("Monoova PayTo Block: No settings found, using defaults"),c={title:"PayTo",description:"Set up PayTo directly from your bank using BSB and Account Number or PayID.",supports:[]});const s=(0,o.__)("PayTo","monoova-payments-for-woocommerce"),d=(0,n.decodeEntities)(c.title)||s,m=({eventRegistration:e,emitResponse:t,billing:n})=>{const{orderId:s}=(0,i.useSelect)((e=>({orderId:e(a.CHECKOUT_STORE_KEY).getOrderId()}))),d=()=>{const e=n?.billingAddress;return!!e&&!!(e.email&&e.first_name&&e.last_name&&e.address_1&&e.city&&e.postcode&&e.country&&e.state&&e.city)},{containerRef:m,isInitialized:p,setContainerInitialized:u,paymentMethod:g,setPaymentMethod:y,payidValue:x,setPayidValue:f,payidType:E,setPayidType:h,accountName:R,setAccountName:b,bsb:_,setBsb:v,accountNumber:C,setAccountNumber:w,maximumAmount:S,setMaximumAmount:I,errors:k,setErrors:A,paymentStatus:F,setPaymentStatus:T,paymentInstructions:z,setPaymentInstructions:P,isSubmitting:B,setIsSubmitting:D,pollInterval:N,setPollInterval:O,hasExpressCheckout:W,setHasExpressCheckout:M,errorDetails:H,setErrorDetails:j,mandateInfo:$,setMandateInfo:L,countdown:V,setCountdown:G,resetStates:U,stopPolling:Y,saveCurrentState:q}=(({settings:e,billing:t,orderId:a,containerId:n="payto-payment-container",paymentMethodId:o="monoova_payto",hasRequiredInfo:i=!0})=>{const[c,s]=(0,r.useState)("payid"),[d,m]=(0,r.useState)(""),[p,u]=(0,r.useState)(""),[g,y]=(0,r.useState)(""),[x,f]=(0,r.useState)(""),[E,h]=(0,r.useState)(""),[R,b]=(0,r.useState)(e.maximum_amount||1e3),[_,v]=(0,r.useState)({}),[C,w]=(0,r.useState)("form"),[S,I]=(0,r.useState)(null),[k,A]=(0,r.useState)(!1),[F,T]=(0,r.useState)(null),[z,P]=(0,r.useState)(!1),[B,D]=(0,r.useState)(null),[N,O]=(0,r.useState)(null),[W,M]=(0,r.useState)(86400),H=((0,r.useRef)(!1),(0,r.useRef)(null)),{targetRef:j,containerElement:$,containerIdActive:L,isContainerInitialized:V,setContainerInitialized:G,setOrderData:U,getOrderData:Y,clearOrderData:q,showContainer:K,hideContainer:X}=((e,t)=>{const a=(0,r.useRef)(null),n=(0,r.useRef)(!1),o=`persistent-${e}-${t}`;return(0,r.useEffect)((()=>{const r=l.getOrCreateContainer(e,t);return r.element&&(r.element.id=o),a.current&&!n.current&&(l.showContainer(e,t,a.current),n.current=!0),()=>{n.current&&(l.hideContainer(e,t),n.current=!1)}}),[e,t]),{targetRef:a,containerElement:l.getContainerElement(e,t),containerIdActive:o,isContainerInitialized:l.isContainerInitialized(e,t),setContainerInitialized:()=>l.setContainerInitialized(e,t),setOrderData:(a,n,o=null)=>l.setOrderData(e,t,a,n,o),getOrderData:()=>l.getOrderData(e,t),clearOrderData:()=>{l.clearOrderData(e,t)},showContainer:()=>{a.current&&!n.current&&(l.showContainer(e,t,a.current),n.current=!0)},hideContainer:()=>{n.current&&(l.hideContainer(e,t),n.current=!1)}}})(o,n),J=Y(),Q=J?.instructions,Z=V&&Q,ee=(0,r.useCallback)((()=>{H.current&&(clearInterval(H.current),H.current=null),F&&(clearInterval(F),T(null))}),[F]);(0,r.useEffect)((()=>{Z&&Q&&(console.log("PayTo: Restoring persistent payment data:",Q),Q.formData&&(s(Q.formData.paymentMethod||"payid"),m(Q.formData.payidValue||""),u(Q.formData.payidType||""),y(Q.formData.accountName||""),f(Q.formData.bsb||""),h(Q.formData.accountNumber||""),b(Q.formData.maximumAmount||e.maximum_amount||1e3)),Q.paymentInstructions&&(I(Q.paymentInstructions),w(Q.paymentStatus||"instructions")),void 0!==Q.hasExpressCheckout&&P(Q.hasExpressCheckout),Q.mandateInfo&&O(Q.mandateInfo))}),[Z,Q,e.maximum_amount]);const te=(0,r.useCallback)((()=>{U(a,null,{formData:{paymentMethod:c,payidValue:d,payidType:p,accountName:g,bsb:x,accountNumber:E,maximumAmount:R},paymentInstructions:S,paymentStatus:C,hasExpressCheckout:z,mandateInfo:N,errors:_,errorDetails:B})}),[c,d,p,g,x,E,R,S,C,z,N,_,B,a,U]);(0,r.useEffect)((()=>{V&&te()}),[te,V]);const ae=(0,r.useCallback)((()=>{s("payid"),m(""),u(""),y(""),f(""),h(""),b(e.maximum_amount||1e3),v({}),w("form"),I(null),A(!1),P(!1),D(null),O(null),M(86400),ee(),q&&q()}),[e.maximum_amount,ee,q]);return(0,r.useEffect)((()=>()=>{ee()}),[ee]),{containerRef:j,containerElement:$,containerIdActive:L,isInitialized:V,setContainerInitialized:G,showContainer:K,hideContainer:X,paymentMethod:c,setPaymentMethod:s,payidValue:d,setPayidValue:m,payidType:p,setPayidType:u,accountName:g,setAccountName:y,bsb:x,setBsb:f,accountNumber:E,setAccountNumber:h,maximumAmount:R,setMaximumAmount:b,errors:_,setErrors:v,paymentStatus:C,setPaymentStatus:w,paymentInstructions:S,setPaymentInstructions:I,isSubmitting:k,setIsSubmitting:A,pollInterval:F,setPollInterval:T,hasExpressCheckout:z,setHasExpressCheckout:P,errorDetails:B,setErrorDetails:D,mandateInfo:N,setMandateInfo:O,countdown:W,setCountdown:M,resetStates:ae,stopPolling:ee,saveCurrentState:te,persistentData:J,shouldUseExistingData:Z}})({settings:c,billing:n,orderId:s,containerId:"payto-payment-container",paymentMethodId:"monoova_payto",hasRequiredInfo:d()}),K=(0,i.useSelect)((e=>{const t=e("wc/store/cart");return t&&t.getCartTotals?t.getCartTotals():null}),[]);(0,r.useEffect)((()=>{const e=document.querySelector(".wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button");return e&&(e.style.display="none"),()=>{e&&(e.style.display="")}}),[]);const X=K?.total_price?parseFloat(K.total_price)/100:0,J=K?.currency_code||c.currency||"AUD",{onPaymentSetup:Q}=e,{responseTypes:Z,noticeContexts:ee}=t;(0,r.useEffect)((()=>{console.log("PayTo: Component mounted, checking express checkout availability..."),te()}),[]),(0,r.useEffect)((()=>()=>{N&&clearInterval(N)}),[N]),(0,r.useEffect)((()=>{const e=setInterval((()=>{G((t=>t<=1?(clearInterval(e),0):t-1))}),1e3);return()=>clearInterval(e)}),[]);const te=async()=>{if(c.is_user_logged_in)try{const e=await fetch(c.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_check_express_checkout",nonce:c.nonce})}),t=await e.json();t.success&&t.data.available?(M(!0),t.data.mandate_info&&L(t.data.mandate_info)):console.log("Express checkout not available or failed:",t)}catch(e){console.error("Error checking express checkout availability:",e)}else console.log("PayTo: User not logged in, skipping express checkout check")},ae=()=>{const e={};return"payid"===g?x.trim()?E||(e.payidValue=(0,o.__)("Please enter a valid email address or phone number.","monoova-payments-for-woocommerce")):e.payidValue=(0,o.__)("PayID is required.","monoova-payments-for-woocommerce"):(R.trim()||(e.accountName=(0,o.__)("Account name is required.","monoova-payments-for-woocommerce")),_.trim()?/^\d{3}-?\d{3}$/.test(_.trim())||(e.bsb=(0,o.__)("Please enter a valid BSB (6 digits).","monoova-payments-for-woocommerce")):e.bsb=(0,o.__)("BSB is required.","monoova-payments-for-woocommerce"),C.trim()||(e.accountNumber=(0,o.__)("Account number is required.","monoova-payments-for-woocommerce"))),(!S||S<=0)&&(e.maximumAmount=(0,o.__)("Maximum amount must be greater than 0.","monoova-payments-for-woocommerce")),A(e),0===Object.keys(e).length};React.useEffect((()=>Q((()=>ae()?{type:Z.SUCCESS,meta:{paymentMethodData:{payto_payment_method:g,payto_payid_type:"payid"===g?E:"",payto_payid_value:"payid"===g?x:"",payto_account_name:"bsb_account"===g?R:"",payto_bsb:"bsb_account"===g?_:"",payto_account_number:"bsb_account"===g?C:"",payto_maximum_amount:S.toString()}}}:{type:Z.ERROR,message:(0,o.__)("Please correct the errors in the PayTo form.","monoova-payments-for-woocommerce"),messageContext:ee.PAYMENTS}))),[Q,g,E,x,R,_,C,S,ae]);const ne=e=>{const t=setInterval((async()=>{try{const a=await fetch(c.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"get_payto_agreement_payment_initiation_status",nonce:c.nonce,order_id:e})}),n=await a.json();if(n.success){const e=n.data.agreement_status||n.data.mandate_status,a=n.data.payment_initiation_status,o=n.data.order_status,r=n.data.error_details;if(console.log("PayTo polling status:",{agreementStatus:e,paymentStatus:a,orderStatus:o,errorDetails:r}),r&&r.requires_new_agreement){clearInterval(t),O(null),T("agreement_limit_exceeded");const e=r.errors||[],a=e.map((e=>e.message)).join(". ");return A({general:a}),void j({message:a,code:"AGREEMENT_LIMIT_EXCEEDED",type:"AGREEMENT_LIMIT_ERROR",context:"payment_initiation",agreement_uid:r.agreement_uid,requires_new_agreement:!0,limit_errors:e})}if(z&&P((t=>({...t,status:e||a||"Processing",agreement_status:e,payment_initiation_status:a,order_status:o,order_key:n.data.order_key||t.order_key,order_received_url:n.data.order_received_url||t.order_received_url}))),"completed"===a||"success"===a||"completed"===o||"processing"===o)clearInterval(t),O(null),T("success"),setTimeout((()=>{const e=n.data.order_received_url||z?.order_received_url;e&&(window.location.href=e)}),2e3);else if("failed"===a||"cancelled"===a||"failed"===e||"cancelled"===e||"rejected"===e)if(clearInterval(t),O(null),"payto_agreement_limit_exceeded"===r?.code||"AGREEMENT_LIMIT_ERROR"===r?.type||"payto_agreement_limit_exceeded"===n.data.error_code)T("agreement_limit_exceeded"),A({general:r?.message||n.data.message||"Payment agreement limits have been exceeded"}),j({message:r?.message||n.data.message||"Payment agreement limits have been exceeded",code:"AGREEMENT_LIMIT_EXCEEDED",type:"AGREEMENT_LIMIT_ERROR",context:"payment_polling",requires_new_agreement:!0,limit_errors:n.data?.errors||r?.limit_errors||[],agreement_status:e,payment_status:a,order_status:o});else{T("failed");let t="Payment was cancelled or failed",n="UNKNOWN_ERROR",r="PAYMENT_FAILED";"rejected"===e?(t="PayTo agreement was rejected by your bank",n="AGREEMENT_REJECTED",r="AGREEMENT_ERROR"):"cancelled"===e?(t="PayTo agreement was cancelled",n="AGREEMENT_CANCELLED",r="AGREEMENT_ERROR"):"failed"===e?(t="PayTo agreement creation failed",n="AGREEMENT_FAILED",r="AGREEMENT_ERROR"):"failed"===a?(t="Payment initiation failed",n="PAYMENT_FAILED",r="PAYMENT_ERROR"):"cancelled"===a&&(t="Payment was cancelled",n="PAYMENT_CANCELLED",r="PAYMENT_ERROR"),A({general:t}),j({message:t,code:n,type:r,context:"payment_polling",agreement_status:e,payment_status:a,order_status:o})}}}catch(e){console.error("Error checking payment status:",e)}}),5e3);O(t)};return"instructions"===F?z?React.createElement(React.Fragment,null,React.createElement("div",{style:{textAlign:"center",margin:"24px 0"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"700",color:"#000",marginBottom:"8px"}},"Pay"," ",React.createElement("span",{style:{fontWeight:"700",color:"#2CB5C5"}},"$",z.amount)),React.createElement("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0"}},"You're about to approve this payment from your banking app.")),React.createElement("div",{style:{marginBottom:"24px"}},React.createElement("div",{style:{display:"flex",gap:"24px",alignItems:"center",marginBottom:"16px"}},React.createElement("div",{style:{fontWeight:"500",fontSize:"18px",lineHeight:"1.21em",color:"#000000",marginBottom:"16px"}},"Pay with"),React.createElement("div",{style:{display:"flex",gap:"16px",alignItems:"center"}},React.createElement("label",{style:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px"}},React.createElement("div",{style:{position:"relative"}},React.createElement("div",{style:{width:"24px",height:"24px",border:"1.5px solid "+("payid"===g?"#2CB5C5":"#ABABAB"),borderRadius:"50%",position:"relative"}},"payid"===g&&React.createElement("div",{style:{width:"14px",height:"14px",backgroundColor:"#2CB5C5",borderRadius:"50%",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}}))),React.createElement("span",{style:{fontWeight:"400",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"PayID")),React.createElement("label",{style:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px"}},React.createElement("div",{style:{position:"relative"}},React.createElement("div",{style:{width:"24px",height:"24px",border:"1.5px solid "+("bsb_account"===g?"#2CB5C5":"#ABABAB"),borderRadius:"50%",position:"relative"}},"bsb_account"===g&&React.createElement("div",{style:{width:"14px",height:"14px",backgroundColor:"#2CB5C5",borderRadius:"50%",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}}))),React.createElement("span",{style:{fontWeight:"400",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"BSB and account number"))))),React.createElement("div",{style:{border:"1px solid #E8E8E8",borderRadius:"16px",padding:"24px 24px 32px",gap:"40px",display:"flex",flexDirection:"column",alignItems:"center"}},"payid"===g&&React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Enter your PayID Email or Mobile number")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid #E8E8E8",borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:x||"",readOnly:!0,placeholder:"Email or mobile number",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:"#9D9C9C",padding:"12px 0"}}))),"bsb_account"===g&&React.createElement(React.Fragment,null,React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Name associated with bank account")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid #E8E8E8",borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:R||"",readOnly:!0,placeholder:"Enter your name",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:"#9D9C9C",padding:"12px 0"}}))),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"BSB")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid #E8E8E8",borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:_||"",readOnly:!0,placeholder:"123-456",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:"#9D9C9C",padding:"12px 0"}}))),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Account Number")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid #E8E8E8",borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:C||"",readOnly:!0,placeholder:"Enter your account number",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:"#9D9C9C",padding:"12px 0"}})))),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column",gap:"12px"}},React.createElement("div",{style:{display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Maximum payment agreement amount")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid #E8E8E8",borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:`$${S||1e3}`,readOnly:!0,style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:"#000000",padding:"12px 0"}}))),React.createElement("div",{style:{fontWeight:"500",fontSize:"14px",lineHeight:"1.29em",color:"#909090"}},"This is the maximum amount that can be charged under this PayTo agreement. You can modify this amount if needed.")),React.createElement("div",{style:{width:"100%",display:"flex",justifyContent:"center",alignItems:"center",gap:"10px",backgroundColor:"#2CB5C5",borderRadius:"8px",minHeight:"48px",opacity:.6,cursor:"not-allowed"}},React.createElement("span",{style:{fontWeight:"700",fontSize:"16px",lineHeight:"1.21em",color:"#000000"}},"Accept and Continue")),React.createElement("div",{style:{width:"100%"}},React.createElement("h4",{style:{fontSize:"18px",fontWeight:"600",color:"#000000",marginBottom:"16px"}},"Authorise recurring payments"),(()=>{z?.status?.toLowerCase();const e=z?.agreement_status?.toLowerCase()||"pending",t=z?.payment_initiation_status?.toLowerCase()||"pending";return"completed"===t||"success"===t||"processed"===t?React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px",borderRadius:"8px",marginBottom:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"#2CB5C5",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0}},React.createElement("svg",{width:"12",height:"9",viewBox:"0 0 12 9",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{d:"M1 4.5L4.5 8L11 1.5",stroke:"#ffffff",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))),React.createElement("span",{style:{fontSize:"16px",color:"#000000",fontWeight:"500"}},"Payment received"))),React.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"14px 10px",backgroundColor:"#E6F7FF",borderRadius:"8px",marginBottom:"16px"}},React.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"#1890FF",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,marginTop:"1px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"12px",fontWeight:"bold"}},"i")),React.createElement("p",{style:{margin:0,fontSize:"14px",color:"#1890FF",lineHeight:"1.5",fontWeight:"500"}},"We will redirect you to Order Received page in"," ",React.createElement("strong",null,"5 sec")))):"authorized"!==e&&"approved"!==e||"initiated"!==t&&"processing"!==t?"failed"===t||"cancelled"===t||"rejected"===t||"failed"===e||"cancelled"===e||"rejected"===e?React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px",textAlign:"center"}},React.createElement("div",{style:{width:"22px",height:"22px",borderRadius:"50%",backgroundColor:"#FF0000",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"14px",fontWeight:"bold"}},"i")),React.createElement("h3",{style:{fontSize:"24px",fontWeight:"400",color:"#000000",margin:"0 0 8px 0"}},"Payment failed"),React.createElement("p",{style:{fontSize:"16px",color:"#000000",margin:"0 0 24px 0",lineHeight:"1.5"}},k.general||H?.message||"Something went wrong with the payment.",React.createElement("br",null),"Please try again."),H&&React.createElement("div",{style:{marginBottom:"24px",padding:"16px",backgroundColor:"#FFF2F2",border:"1px solid #FFCDD2",borderRadius:"8px",textAlign:"left",fontSize:"14px"}},React.createElement("h4",{style:{margin:"0 0 12px 0",color:"#D32F2F",fontSize:"14px",fontWeight:"600"}},"Error Details"),H.code&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Code:")," ",React.createElement("span",{style:{color:"#666"}},H.code)),H.type&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Type:")," ",React.createElement("span",{style:{color:"#666"}},H.type)),H.context&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Context:")," ",React.createElement("span",{style:{color:"#666"}},H.context)),H.validation_errors&&React.createElement("div",{style:{marginTop:"12px",fontSize:"13px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Validation Errors:"),React.createElement("div",{style:{marginTop:"4px",color:"#666"}},"object"==typeof H.validation_errors?Object.entries(H.validation_errors).map((([e,t],a)=>React.createElement("div",{key:a,style:{marginBottom:"4px"}},React.createElement("strong",null,e,":")," ",t))):H.validation_errors))),React.createElement("button",{onClick:()=>{T("form"),A({}),j(null),P(null)},style:{width:"100%",padding:"12px 20px",backgroundColor:"#2CB5C5",color:"#000000",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"700",lineHeight:"1.21em"}},"Pay Again"))):React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"14px 10px",backgroundColor:"#EDFDFF",borderRadius:"8px",marginBottom:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"#007A89",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,marginTop:"1px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"12px",fontWeight:"bold"}},"!")),React.createElement("p",{style:{margin:0,fontSize:"14px",color:"#007A89",lineHeight:"1.5",fontWeight:"500"}},"Approve your recurring payment in your online banking or banking app!")),React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px",borderRadius:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",border:"2px solid #ffffff",borderTop:"2px solid #007A89",borderRadius:"50%",animation:"spin 1s linear infinite",flexShrink:0}}),React.createElement("span",{style:{fontSize:"16px",color:"#000000",fontWeight:"500"}},"Awaiting your approval")))):React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px",borderRadius:"8px",marginBottom:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",border:"2px solid #ffffff",borderTop:"2px solid #2CB5C5",borderRadius:"50%",animation:"spin 1s linear infinite",flexShrink:0}}),React.createElement("span",{style:{fontSize:"16px",color:"#000000",fontWeight:"500"}},"Initiate payment"))))})()))):null:"agreement_limit_exceeded"===F?React.createElement(React.Fragment,null,React.createElement("div",null,React.createElement("div",{style:{textAlign:"center",margin:"24px 0"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"700",color:"#000",marginBottom:"8px"}},"Payment Agreement Limit Exceeded"),React.createElement("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0"}},"Your current payment agreement has reached its limits.")),React.createElement("div",{style:{backgroundColor:"#FFF2F2",border:"1px solid #FFCDD2",borderRadius:"8px",padding:"16px",marginBottom:"24px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},React.createElement("span",{style:{fontSize:"18px",marginRight:"8px"}},"⚠️"),React.createElement("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#D32F2F",margin:"0"}},"Agreement Limits Exceeded")),React.createElement("p",{style:{fontSize:"16px",color:"#000000",margin:"0 0 16px 0",lineHeight:"1.5"}},k.general||H?.message||"Payment agreement limits have been exceeded."),H?.limit_errors&&H.limit_errors.length>0&&React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("strong",{style:{color:"#D32F2F",fontSize:"14px"}},"Specific Issues:"),React.createElement("ul",{style:{margin:"8px 0 0 20px",color:"#666",fontSize:"14px"}},H.limit_errors.map(((e,t)=>React.createElement("li",{key:t,style:{marginBottom:"4px"}},e.message))))),React.createElement("p",{style:{fontSize:"14px",color:"#666",margin:"0"}},"To continue with your payment, you'll need to create a new payment agreement with updated limits.")),React.createElement("button",{onClick:async()=>{D(!0),A({}),j(null);try{const e=n?.billingAddress||{},t=await fetch(c.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_payto_reset_agreement",nonce:c.nonce,order_id:z?.order_id||s,customer_email:e.email||""})}),a=await t.json();a.success?(P(null),T("form"),A({}),j(null),y("payid"),h("email"),f(""),b(""),v(""),w(""),I(X)):A({general:a.data?.message||"Failed to reset payment agreement"})}catch(e){A({general:"Network error occurred while resetting payment agreement"})}finally{D(!1)}},disabled:B,style:{width:"100%",padding:"12px 20px",backgroundColor:B?"#ccc":"#2CB5C5",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"600",cursor:B?"not-allowed":"pointer",transition:"background-color 0.2s",marginBottom:"16px"},onMouseOver:e=>{B||(e.target.style.backgroundColor="#1a9aa8")},onMouseOut:e=>{B||(e.target.style.backgroundColor="#2CB5C5")}},B?React.createElement(React.Fragment,null,React.createElement("span",{style:{display:"inline-block",width:"16px",height:"16px",border:"2px solid #ffffff",borderTop:"2px solid transparent",borderRadius:"50%",animation:"spin 1s linear infinite",marginRight:"8px"}}),"Resetting Agreement..."):"Pay Again with New Agreement"))):"failed"===F?React.createElement(React.Fragment,null,React.createElement("div",null,React.createElement("div",{style:{textAlign:"center",margin:"24px 0"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"700",color:"#000",marginBottom:"8px"}},"Pay"," ",React.createElement("span",{style:{fontWeight:"700",color:"#2CB5C5"}},"$",X.toFixed(2))),React.createElement("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0"}},"You're about to approve this payment from your banking app.")),React.createElement("div",{style:{border:"1px solid #E8E8E8",borderRadius:"16px",padding:"24px 24px 32px",gap:"16px",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center"}},React.createElement("div",{style:{width:"45px",height:"45px",borderRadius:"50%",backgroundColor:"#FF0000",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"24px",fontWeight:"bold"}},"i")),React.createElement("h3",{style:{fontSize:"24px",fontWeight:"600",color:"#000000",margin:"0 0 8px 0"}},"Payment failed"),React.createElement("p",{style:{fontSize:"16px",color:"#000000",margin:"0 0 24px 0",lineHeight:"1.5"}},k.general||H?.message||"Something went wrong with the payment."),H&&React.createElement("div",{style:{marginBottom:"24px",padding:"16px",backgroundColor:"#FFF2F2",border:"1px solid #FFCDD2",borderRadius:"8px",textAlign:"left",fontSize:"14px"}},React.createElement("h4",{style:{margin:"0 0 12px 0",color:"#D32F2F",fontSize:"14px",fontWeight:"600"}},"Error Details"),H.code&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Code:")," ",React.createElement("span",{style:{color:"#666"}},H.code)),H.type&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Type:")," ",React.createElement("span",{style:{color:"#666"}},H.type)),H.context&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Context:")," ",React.createElement("span",{style:{color:"#666"}},H.context)),(H.agreement_status||H.payment_status||H.order_status)&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Status:")," ",React.createElement("span",{style:{color:"#666"}},[H.agreement_status,H.payment_status,H.order_status].filter(Boolean).join(", "))),H.details&&React.createElement("div",{style:{marginTop:"12px",fontSize:"13px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Technical Details:"),React.createElement("div",{style:{marginTop:"4px",padding:"8px",backgroundColor:"#FFFFFF",border:"1px solid #E0E0E0",borderRadius:"4px",color:"#666",fontFamily:"monospace",fontSize:"12px",wordBreak:"break-word"}},"string"==typeof H.details?H.details:JSON.stringify(H.details,null,2))),H.validation_errors&&React.createElement("div",{style:{marginTop:"12px",fontSize:"13px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Validation Errors:"),React.createElement("div",{style:{marginTop:"4px",color:"#666"}},"object"==typeof H.validation_errors?Object.entries(H.validation_errors).map((([e,t],a)=>React.createElement("div",{key:a,style:{marginBottom:"4px"}},React.createElement("strong",null,e,":")," ",t))):H.validation_errors))),React.createElement("button",{onClick:()=>{T("form"),A({}),j(null),P(null)},style:{width:"100%",padding:"12px 20px",backgroundColor:"#2CB5C5",color:"#000000",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"700",lineHeight:"1.21em"}},"Pay Again")))):React.createElement(React.Fragment,null,React.createElement("div",{ref:m},React.createElement("div",{style:{textAlign:"center",margin:"24px 0"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"700",color:"#000",marginBottom:"8px"}},"Pay ",React.createElement("span",{style:{fontWeight:"700",color:"#2CB5C5"}},"$",X.toFixed(2))),React.createElement("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0"}},"You're about to approve this payment from your banking app.")),!d()&&React.createElement("div",{style:{padding:"16px",backgroundColor:"#f3f4f6",borderRadius:"8px",margin:"16px 0",textAlign:"center"}},React.createElement("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#374151"}},"Please complete your billing information to initialize the payment form."),React.createElement("small",{style:{fontSize:"12px",color:"#6b7280"}},"Required: Email, Name, Address, City, Postcode, and Country")),W&&c.is_user_logged_in&&React.createElement("div",{style:{marginBottom:"20px",textAlign:"center"}},$&&React.createElement("div",{style:{marginBottom:"16px",padding:"16px",backgroundColor:"#f0f8ff",border:"1px solid #0073aa",borderRadius:"8px",textAlign:"left"}},React.createElement("h4",{style:{margin:"0 0 12px 0",color:"#0073aa",fontSize:"16px"}},(0,o.__)("Express Checkout Available","monoova-payments-for-woocommerce")),React.createElement("p",{style:{margin:"0 0 12px 0",color:"#666",fontSize:"14px"}},(0,o.__)("You have an active PayTo mandate that can be used for faster checkout.","monoova-payments-for-woocommerce")),React.createElement("div",{style:{backgroundColor:"#ffffff",padding:"12px",borderRadius:"4px",fontSize:"13px",color:"#333"}},React.createElement("div",{style:{marginBottom:"6px"}},React.createElement("strong",null,(0,o.__)("Agreement:","monoova-payments-for-woocommerce"))," ",$.agreement_id?`...${$.agreement_id.slice(-8)}`:"Available"),React.createElement("div",{style:{marginBottom:"6px"}},React.createElement("strong",null,(0,o.__)("Maximum Amount:","monoova-payments-for-woocommerce"))," ",J," $",parseFloat($.maximum_amount||0).toFixed(2)),React.createElement("div",{style:{marginBottom:"6px"}},React.createElement("strong",null,(0,o.__)("Status:","monoova-payments-for-woocommerce"))," ",React.createElement("span",{style:{color:"authorized"===$.status?"#10b981":"#f59e0b",fontWeight:"500"}},$.status||"Active")),$.num_of_transactions_permitted&&React.createElement("div",null,React.createElement("strong",null,(0,o.__)("Transactions:","monoova-payments-for-woocommerce"))," ",React.createElement("span",{style:{color:$.remaining_transactions>0?"#10b981":"#ef4444",fontWeight:"500"}},$.remaining_transactions||0," remaining")," ",React.createElement("span",{style:{color:"#6b7280",fontSize:"12px"}},"(",$.num_of_paid_transactions||0,"/",$.num_of_transactions_permitted||10," used)")))),React.createElement("button",{type:"button",onClick:async()=>{D(!0),T("processing"),A({}),j(null);try{const e=await fetch(c.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_process_payment_with_existing_mandate",nonce:c.nonce,order_id:s,payment_agreement_uid:$?.agreement_id})}),t=await e.json();if(t.success)P({order_id:t.data.order_id,currency:J,amount:X.toFixed(2),status:"Processing",agreement_reference:t.data.order_id,message:"Payment initiated with existing mandate"}),T("instructions"),ne(t.data.order_id);else{const e=t.data?.message||"Express checkout failed",a=t.data?.error_code||"UNKNOWN_ERROR",n=t.data?.error_type||"EXPRESS_CHECKOUT_ERROR";"payto_agreement_limit_exceeded"===a||"AGREEMENT_LIMIT_ERROR"===n?(T("agreement_limit_exceeded"),A({general:e}),j({message:e,code:"AGREEMENT_LIMIT_EXCEEDED",type:"AGREEMENT_LIMIT_ERROR",context:"express_checkout",requires_new_agreement:!0,limit_errors:t.data?.errors||[],agreement_uid:t.data?.agreement_uid})):(T("failed"),A({general:e}),j({message:e,code:a,type:n,context:"express_checkout",details:t.data?.error_details||null}))}}catch(e){T("failed"),A({general:"Network error occurred during express checkout"}),j({message:"Network error occurred during express checkout",code:"NETWORK_ERROR",type:"CONNECTION_ERROR",context:"express_checkout",details:e.message})}finally{D(!1)}},disabled:B,style:{width:"100%",padding:"12px 20px",backgroundColor:B?"#9ca3af":"#10b981",color:"#ffffff",border:"none",borderRadius:"8px",cursor:B?"not-allowed":"pointer",fontSize:"14px",fontWeight:"600",marginBottom:"12px"}},B?(0,o.__)("Processing...","monoova-payments-for-woocommerce"):(0,o.__)("Pay with existing PayTo mandate","monoova-payments-for-woocommerce")),React.createElement("div",{style:{fontSize:"12px",color:"#6b7280",marginBottom:"16px"}},(0,o.__)("or set up a new payment method below","monoova-payments-for-woocommerce"))),d()&&React.createElement(React.Fragment,null,React.createElement("div",{style:{marginBottom:"24px"}},React.createElement("div",{style:{display:"flex",gap:"24px",alignItems:"center",marginBottom:"16px"}},React.createElement("div",{style:{fontWeight:"500",fontSize:"18px",lineHeight:"1.21em",color:"#000000",marginBottom:"16px"}},"Pay with"),React.createElement("div",{style:{display:"flex",gap:"16px",alignItems:"center"}},React.createElement("label",{style:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px"}},React.createElement("div",{style:{position:"relative"}},React.createElement("div",{style:{width:"24px",height:"24px",border:"1.5px solid "+("payid"===g?"#2CB5C5":"#ABABAB"),borderRadius:"50%",position:"relative"}},"payid"===g&&React.createElement("div",{style:{width:"14px",height:"14px",backgroundColor:"#2CB5C5",borderRadius:"50%",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}})),React.createElement("input",{type:"radio",name:"payto_payment_method",value:"payid",checked:"payid"===g,onChange:e=>y(e.target.value),style:{display:"none"}})),React.createElement("span",{style:{fontWeight:"400",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"PayID")),React.createElement("label",{style:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px"}},React.createElement("div",{style:{position:"relative"}},React.createElement("div",{style:{width:"24px",height:"24px",border:"1.5px solid "+("bsb"===g?"#2CB5C5":"#ABABAB"),borderRadius:"50%",position:"relative"}},"bsb_account"===g&&React.createElement("div",{style:{width:"14px",height:"14px",backgroundColor:"#2CB5C5",borderRadius:"50%",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}})),React.createElement("input",{type:"radio",name:"payto_payment_method",value:"bsb_account",checked:"bsb_account"===g,onChange:e=>y(e.target.value),style:{display:"none"}})),React.createElement("span",{style:{fontWeight:"400",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"BSB and account number"))))),React.createElement("div",{style:{border:"1px solid #E8E8E8",borderRadius:"16px",padding:"24px 24px 32px",gap:"16px",display:"flex",flexDirection:"column",alignItems:"center"}},"payid"===g&&React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Enter your PayID Email or Mobile number")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid "+(k.payidValue?"#ef4444":"#E8E8E8"),borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:x,onChange:e=>{const t=e.target.value;f(t),(e=>{if(!e.trim())return h(""),void A((e=>({...e,payidValue:""})));/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?(h("Email"),A((e=>({...e,payidValue:""})))):/^(\+61|0)[0-9]{9}$/.test(e)?(h("PhoneNumber"),A((e=>({...e,payidValue:""})))):(h(""),A((e=>({...e,payidValue:(0,o.__)("Please enter a valid email address or phone number.","monoova-payments-for-woocommerce")}))))})(t)},placeholder:"Email or mobile number",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:k.payidValue?"#ef4444":"#9D9C9C",padding:"12px 0"}})),k.payidValue&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.payidValue)),"bsb_account"===g&&React.createElement(React.Fragment,null,React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Name associated with bank account")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid "+(k.accountName?"#ef4444":"#E8E8E8"),borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:R,onChange:e=>b(e.target.value),placeholder:"Enter your name",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:k.accountName?"#ef4444":"#9D9C9C",padding:"12px 0"}})),k.accountName&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.accountName)),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"BSB")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid "+(k.bsb?"#ef4444":"#E8E8E8"),borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:_,onChange:e=>v(e.target.value),placeholder:"123-456",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:k.bsb?"#ef4444":"#9D9C9C",padding:"12px 0"}})),k.bsb&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.bsb)),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Account Number")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid "+(k.accountNumber?"#ef4444":"#E8E8E8"),borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"text",value:C,onChange:e=>w(e.target.value),placeholder:"Enter your account number",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:k.accountNumber?"#ef4444":"#9D9C9C",padding:"12px 0"}})),k.accountNumber&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.accountNumber))),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column",gap:"12px"}},React.createElement("div",{style:{display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Maximum payment agreement amount")),React.createElement("div",{style:{display:"flex",alignItems:"stretch",gap:"10px",padding:"0px 12px",backgroundColor:"#FAFAFA",border:"1px solid "+(k.maximumAmount?"#ef4444":"#E8E8E8"),borderRadius:"8px",minHeight:"48px"}},React.createElement("input",{type:"number",value:S,onChange:e=>I(parseFloat(e.target.value)||0),min:"0.01",step:"0.01",placeholder:"$1,000",style:{flex:1,border:"none",backgroundColor:"transparent",outline:"none",fontWeight:"500",fontSize:"16px",lineHeight:"1.21em",color:"#000000",padding:"12px 0"}})),k.maximumAmount&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.maximumAmount)),React.createElement("div",{style:{fontWeight:"500",fontSize:"14px",lineHeight:"1.29em",color:"#909090"}},"This is the maximum amount that can be charged under this PayTo agreement. You can modify this amount if needed.")),React.createElement("div",{style:{width:"100%",display:"flex",justifyContent:"center",alignItems:"center",gap:"10px",padding:"0px 12px",backgroundColor:"#2CB5C5",borderRadius:"8px",minHeight:"48px",cursor:B?"not-allowed":"pointer"},onClick:B?void 0:async e=>{if(e&&(e.preventDefault(),e.stopPropagation()),ae()){D(!0),T("processing"),A({}),j(null),L(null),M(!1);try{let e=n?.billingAddress||{};const t=await fetch(c.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_create_payto_agreement",nonce:c.nonce,payto_payment_method:g,payto_payid_type:"payid"===g?E:"",payto_payid_value:"payid"===g?x:"",payto_account_name:"bsb_account"===g?R:"",payto_bsb:"bsb_account"===g?_:"",payto_account_number:"bsb_account"===g?C:"",payto_maximum_amount:S.toString(),billing_first_name:e.first_name||"",billing_last_name:e.last_name||"",billing_email:e.email||"",order_id:s,isCheckoutPage:!0})}),a=await t.json();if(a.success)P({order_id:a.data.order_id,order_key:a.data.order_key,order_received_url:a.data.order_received_url,currency:J,amount:X.toFixed(2),status:"Processing",agreement_reference:a.data.order_id,message:"Payment agreement created successfully"}),T("instructions"),ne(a.data.order_id);else{const e=a.data?.message||"Failed to create payment agreement",t=a.data?.error_code||"UNKNOWN_ERROR",n=a.data?.error_type||"AGREEMENT_CREATION_ERROR";T("failed"),A({general:e}),j({message:e,code:t,type:n,context:"agreement_creation",details:a.data?.error_details||null,validation_errors:a.data?.validation_errors||null})}}catch(e){T("failed"),A({general:"Network error occurred while creating payment agreement"}),j({message:"Network error occurred while creating payment agreement",code:"NETWORK_ERROR",type:"CONNECTION_ERROR",context:"agreement_creation",details:e.message})}finally{D(!1)}}}},React.createElement("span",{style:{fontWeight:"700",fontSize:"16px",lineHeight:"1.21em",color:"#000000"}},B||"processing"===F?"Processing...":"Accept and Continue"))))))},p=function(e){return React.createElement(m,e)},u=e=>React.createElement("div",{style:{width:"100%"}},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"}},React.createElement("span",{style:{fontWeight:600}},d),React.createElement("img",{src:`${c.plugin_url||"/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/payto-logo.svg`,alt:"PayTo logo",style:{height:"24px",width:"auto"}})),c.description&&React.createElement("div",{style:{marginTop:"8px",fontSize:"14px",color:"#6b7280",lineHeight:"1.4"},dangerouslySetInnerHTML:{__html:(0,n.decodeEntities)(c.description||"")}})),g={name:"monoova_payto",label:React.createElement(u,null),content:React.createElement(p,null),edit:React.createElement(p,null),canMakePayment:()=>!0,ariaLabel:d,supports:{features:c.supports||[]},icons:c.icons||null};try{(0,e.registerPaymentMethod)(g)}catch(e){console.error("Monoova PayTo Block: Failed to register payment method:",e)}}})();